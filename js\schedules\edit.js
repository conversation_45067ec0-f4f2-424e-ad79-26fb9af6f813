+ function ($) {
	'use strict';

	function addItemRow() {
		var $newRow = $(itemRow());

		var $itemTable = $('.item-table');
		var tripItems = $itemTable.find('.trip_item');

		if (tripItems.length === 0) {
			$itemTable.find('.no-item').remove();
		}
		$itemTable.find('tbody').append($newRow);

		setRowNumbers();
	}

	function itemRow() {
		var rowId = makeRandomId();

		return '<tr class="trip_item">' +
			'<td>' +
				'<input type="hidden" name="trip_item_new_' + rowId + '" value="' + rowId + '">' +
				'<input type="number" name="trip_no_new_' + rowId + '" class="form-control-plaintext" value="" readonly>' +
			'</td>' +
			'<td>' +
				'<input type="time" name="eta_time_new_' + rowId + '" class="form-control" value="">' +
			'</td>' +
			'<td class="text-center">' +
				'<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>' +
			'</td>' +
		'</tr>';
	}

	function makeRandomId() {
		var text = '';
		var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

		for (var i = 0; i < 32; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}

		return text;
	}

	function emptyRow() {
		return '<tr class="no-item">' +
				'<td class="text-center" colspan="3">No trips added yet.</td>' +
			'</tr>';
	}

	function setRowNumbers() {
		var customerId = $('select[name="customer"]').val();
		var etaDate = $('input[name="eta_date"]').val();

		if (customerId && etaDate) {
			// Get next trip number from server
			$.ajax({
				url: BASE_URI + 'schedules/get_next_trip_number',
				type: 'POST',
				data: {
					customer_id: customerId,
					eta_date: etaDate
				},
				dataType: 'json',
				success: function(response) {
					if (response.next_trip_no) {
						var $itemTable = $('.item-table');
						var tripItems = $itemTable.find('.trip_item');
						var startingTripNo = response.next_trip_no;

						// Only update new trips (those with name starting with "trip_no_new_")
						var newTripIndex = 0;
						tripItems.each(function (_, row) {
							var $tripNoInput = $(row).find('input[name^="trip_no_"]');
							var inputName = $tripNoInput.attr('name');

							if (inputName && inputName.indexOf('trip_no_new_') === 0) {
								// This is a new trip, assign the next available trip number
								$tripNoInput.val(startingTripNo + newTripIndex);
								newTripIndex++;
							}
							// Existing trips keep their original trip numbers
						});
					}
				},
				error: function() {
					// Fallback to sequential numbering if AJAX fails
					var $itemTable = $('.item-table');
					var tripItems = $itemTable.find('.trip_item');

					tripItems.each(function (index, row) {
						var $tripNoInput = $(row).find('input[name^="trip_no_"]');
						var inputName = $tripNoInput.attr('name');

						if (inputName && inputName.indexOf('trip_no_new_') === 0) {
							$tripNoInput.val(index + 1);
						}
					});
				}
			});
		} else {
			// Fallback to sequential numbering if no customer/date selected
			var $itemTable = $('.item-table');
			var tripItems = $itemTable.find('.trip_item');

			tripItems.each(function (index, row) {
				var $tripNoInput = $(row).find('input[name^="trip_no_"]');
				var inputName = $tripNoInput.attr('name');

				if (inputName && inputName.indexOf('trip_no_new_') === 0) {
					$tripNoInput.val(index + 1);
				}
			});
		}
	}

	function removeItemRow(event) {
		var $row = $(event.currentTarget).closest('.trip_item');

		// Check if this is an existing trip (has trip_item_ without new_)
		var $hiddenInput = $row.find('input[name^="trip_item_"]:not([name*="new_"])');
		if ($hiddenInput.length > 0) {
			// This is an existing trip, mark it for deletion
			var tripId = $hiddenInput.val();
			$('<input>').attr({
				type: 'hidden',
				name: 'deleted_trip_' + tripId,
				value: tripId
			}).appendTo($row.closest('form'));
		}

		$row.remove();

		var $itemTable = $(event.delegateTarget);
		var tripItems = $itemTable.find('.trip_item');
		if (tripItems.length < 1) {
			var $emptyRow = $(emptyRow());
			$itemTable.find('tbody').html($emptyRow);
		}

		setRowNumbers();
	}

	$(function () {
		$('.action-add-item').click(addItemRow);

		$('.item-table')
			.on('click', '.action-delete-item', function (event) {
				removeItemRow(event)
			});

		// Update trip numbers when customer or date changes
		$('select[name="customer"], input[name="eta_date"]').on('change', function() {
			setRowNumbers();
		});

		// Set initial row numbers for existing trips
		setRowNumbers();
	});

}(jQuery);
