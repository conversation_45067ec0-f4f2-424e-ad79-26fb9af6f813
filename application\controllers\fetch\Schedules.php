<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Schedules extends MYT_Controller
{
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();
        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }
        $this->load->helper('datatables_ssp');
    }

    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname,
            'port' => 3307
        ];
        $table = 'schedule';
        $primary_key = 'schedule.id';
        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_trip_id(),
            $this->_get_schedule_id(),
            $this->_get_customer_name(),
            $this->_get_waf_rs_no(),
            $this->_get_eta_date(),
            $this->_get_eta_time(),
            $this->_get_actions()
        ];

        $joinQuery = "schedule LEFT JOIN customer ON customer.id = schedule.customer_id LEFT JOIN schedule_list ON schedule_list.schedule_id = schedule.id";
        $where = 'schedule.is_deleted = 0 AND schedule_list.is_deleted = 0';

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joinQuery, $where)
        );
    }

    protected function _get_id()
    {
        return [
            'db' => 'schedule.id',
            'as' => 'id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    protected function _get_class()
    {
        return [
            'db' => 'schedule.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'schedule';
            }
        ];
    }

    protected function _get_trip_id()
    {
        return [
            'db' => 'schedule_list.id',
            'as' => 'trip_id',
            'dt' => 'DT_RowData',
            'field' => 'trip_id'
        ];
    }

    protected function _get_schedule_id()
    {
        return ['db' => 'schedule_list.trip_no', 'dt' => 0, 'field' => 'trip_no'];
    }

    protected function _get_customer_name()
    {
        return ['db' => 'customer.name', 'as' => 'customer_name', 'dt' => 1, 'field' => 'customer_name'];
    }

    protected function _get_waf_rs_no()
    {
        return ['db' => 'schedule.waf_rs_no', 'dt' => 2, 'field' => 'waf_rs_no'];
    }

    protected function _get_eta_date()
    {
        return ['db' => 'schedule.eta_date', 'dt' => 3, 'field' => 'eta_date'];
    }

    protected function _get_eta_time()
    {
        return ['db' => 'schedule_list.eta_time', 'as' => 'eta_time', 'dt' => 4, 'field' => 'eta_time'];
    }

    protected function _get_actions()
    {
        return [
            'db' => 'schedule.id',
            'as' => 'actions',
            'dt' => 6,
            'field' => 'actions',
            'formatter' => function ($d, $row) {
                $trip_id = isset($row['trip_id']) ? $row['trip_id'] : '';
                $res = '<div class="dropdown text-right">';
                $res .= '<button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown">Actions <span class="caret"></span></button>';
                $res .= '<div class="dropdown-menu dropdown-menu-right">';
                $res .= anchor('schedules/edit/' . $d, 'Reschedule', 'title="Reschedule" class="dropdown-item text-left"');
                if (!empty($trip_id)) {
                    $res .= '<button class="action-delete dropdown-item text-left red-text" role="button" data-id="' . $trip_id . '">Delete Trip</button>';
                }
                $res .= '</div>';
                $res .= '</div>';

                return $res;
            }
        ];
    }
}