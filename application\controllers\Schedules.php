<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Schedules extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('Schedule');
        $this->load->model('Schedule_list');
        $this->load->model('Customer');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Schedules';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/schedules/manager'
        ];
        $this->build_content('default', 'schedules/manager');
    }

    /**
     * Add schedule main
     */
    public function add()
    {
        $rules = $this->config->item('schedules/add');

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('schedules'));
        } else {
            $this->title = 'Add Schedule';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/schedules/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $data = [
                'form_error' => $form_error,
                'customers' => $this->Customer->select()
            ];
            $this->build_content('default', 'schedules/add', $data);
        }
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $customer_id = $this->input->post('customer');
        $waf_rs_no = $this->input->post('waf_rs_no');
        $eta_date_input = $this->input->post('eta_date');

        $eta_date = date('Y-m-d H:i:s', strtotime($eta_date_input));

        // Validate customer exists
        $customer = $this->Customer->select('name', ['id' => $customer_id], 1);
        if (!$customer) {
            $this->_error = 'invalid_customer';
            return false;
        }

        // Prepare the schedule data
        $values = [
            'customer_id' => $customer_id,
            'waf_rs_no' => $waf_rs_no,
            'eta_date' => $eta_date,
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        // Insert the schedule
        if (!$schedule_id = $this->Schedule->insert($values)) {
            $this->_error = 'server_error';
            return false;
        }

        // Process trip data
        $this->_process_trip_data($schedule_id);

        return $schedule_id;
    }
   

    /**
     * Process trip data
     */
    protected function _process_trip_data($schedule_id)
    {
        $post_data = $this->input->post();

        foreach ($post_data as $key => $value) {
            if (strpos($key, 'trip_item_') === 0) {
                $trip_id = $value;
                $trip_no = $this->input->post('trip_no_' . $trip_id);
                $eta_time = $this->input->post('eta_time_' . $trip_id);

                if (!empty($eta_time)) {
                    $trip_values = [
                        'schedule_id' => $schedule_id,
                        'trip_no' => $trip_no,
                        'eta_time' => $eta_time,
                        'added_by' => $_SESSION['user']->id,
                        'added_on' => (new DateTime())->format('Y-m-d H:i:s')
                    ];

                    $this->Schedule_list->insert($trip_values);
                }
            }
        }
    }

    /**
     * Delete Schedule
     */
    public function delete($schedule_id)
    {
        $where = [
            'id' => $schedule_id,
            'is_deleted' => 0
        ];
        if (!$schedule = $this->Schedule->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Schedule is not found.'
            ];
        } elseif (!$this->_attempt_delete($schedule)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Schedule is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Attempt Delete
     */
    protected function _attempt_delete($schedule)
    {
        $where = [
            'id' => $schedule->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->Schedule->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }
}
