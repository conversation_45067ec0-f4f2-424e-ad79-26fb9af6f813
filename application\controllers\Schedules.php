<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 *
 * This class object extends the MYT controller class.
 *
 * @package     Leyte Agri Ventures Corp.
 * @subpackage  Application
 * @category    Controllers
 * <AUTHOR> SoftDev Solutions
 * @link        http://mytsoftdevsolutions.com
 */

class Schedules extends MYT_Controller
{

    protected $_error = '';

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->_load_essentials();
    }

    /**
     * Load Pre-requisite Models, Config Files, and Libraries
     */
    protected function _load_essentials()
    {
        $this->load->model('Schedule');
        $this->load->model('Schedule_list');
        $this->load->model('Customer');
        $this->lang->load('error_messages', 'english');
        $this->config->load('validation_rules');
    }

    /**
     * Index
     */
    public function index()
    {
        $this->title = 'Schedules';
        $this->css = [
            'lib/datatables/datatables/css/dataTables.bootstrap4',
            'lib/datatables/buttons/css/buttons.dataTables',
            'lib/datatables/buttons/css/buttons.bootstrap4',
            'lib/alertify/build/css/alertify'
        ];
        $this->javascript = [
            'lib/datatables/datatables/js/jquery.dataTables',
            'lib/datatables/datatables/js/dataTables.bootstrap4',
            'lib/datatables/buttons/js/dataTables.buttons',
            'lib/datatables/buttons/js/buttons.bootstrap4',
            'lib/alertify/build/alertify',
            'js/schedules/manager'
        ];
        $this->build_content('default', 'schedules/manager');
    }

    /**
     * Add schedule main
     */
    public function add()
    {
        $rules = $this->config->item('schedules/add');

        if ($this->_validate_form($rules) && $this->_attempt_add()) {
            redirect(site_url('schedules'));
        } else {
            $this->title = 'Add Schedule';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/schedules/add'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            $data = [
                'form_error' => $form_error,
                'customers' => $this->Customer->select()
            ];
            $this->build_content('default', 'schedules/add', $data);
        }
    }

    /**
     * Edit/Reschedule trip
     */
    public function edit($trip_id)
    {
        // Get the trip first
        $trip_where = [
            'id' => $trip_id,
            'is_deleted' => 0
        ];
        $trip = $this->Schedule_list->select('', $trip_where, 1) or show_404();

        // Get the parent schedule
        $schedule_where = [
            'id' => $trip->schedule_id,
            'is_deleted' => 0
        ];
        $schedule = $this->Schedule->select('', $schedule_where, 1) or show_404();

        $rules = $this->config->item('schedules/edit');

        if ($this->_validate_form($rules) && $this->_attempt_edit_trip($schedule, $trip)) {
            redirect(site_url('schedules'));
        } else {
            $this->title = 'Reschedule';
            $this->css = [
                'lib/select2/dist/css/select2'
            ];
            $this->javascript = [
                'lib/select2/dist/js/select2.full',
                'js/schedules/edit'
            ];

            if (($form_error = $this->_error_msg()) === validation_errors()) {
                $form_error = '';
            }

            // Get the specific trip data
            $trip_data = [$trip]; // Only the single trip being edited

            $data = [
                'form_error' => $form_error,
                'schedule' => $schedule,
                'customers' => $this->Customer->select(),
                'trip_data' => $trip_data
            ];
            $this->build_content('default', 'schedules/edit', $data);
        }
    }

    /**
     * Validate Form
     */
    protected function _validate_form($rules = null)
    {
        if (!$rules) {
            $this->_error = 'no_validation_rule';
            return false;
        }
        $this->form_validation->set_rules($rules);

        if (!$valid = $this->form_validation->run()) {
            $this->_error = 'validation_error';
        }

        return $valid;
    }

    /**
     * Get Error Message
     */
    protected function _error_msg()
    {
        switch ($this->_error) {
            case 'validation_error':
                return validation_errors();

            default:
                $error = $this->lang->line($this->_error) ?: $this->_error;
                return '<p>' . $error . '</p>';
        }
    }

    /**
     * Attempt Add
     */
    protected function _attempt_add()
    {
        $customer_id = $this->input->post('customer');
        $waf_rs_no = $this->input->post('waf_rs_no');
        $eta_date_input = $this->input->post('eta_date');

        $eta_date = date('Y-m-d H:i:s', strtotime($eta_date_input));

        // Validate customer exists
        $customer = $this->Customer->select('name', ['id' => $customer_id], 1);
        if (!$customer) {
            $this->_error = 'invalid_customer';
            return false;
        }

        // Prepare the schedule data
        $values = [
            'customer_id' => $customer_id,
            'waf_rs_no' => $waf_rs_no,
            'eta_date' => $eta_date,
            'added_by' => $_SESSION['user']->id,
            'added_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        // Insert the schedule
        if (!$schedule_id = $this->Schedule->insert($values)) {
            $this->_error = 'server_error';
            return false;
        }

        // Process trip data
        $this->_process_trip_data($schedule_id);

        return $schedule_id;
    }

    /**
     * Attempt Edit
     */
    protected function _attempt_edit($schedule)
    {
        $customer_id = $this->input->post('customer');
        $waf_rs_no = $this->input->post('waf_rs_no');
        $eta_date_input = $this->input->post('eta_date');

        $eta_date = date('Y-m-d H:i:s', strtotime($eta_date_input));

        // Validate customer exists
        $customer = $this->Customer->select('name', ['id' => $customer_id], 1);
        if (!$customer) {
            $this->_error = 'invalid_customer';
            return false;
        }

        $where = [
            'id' => $schedule->id,
            'is_deleted' => 0
        ];

        // Prepare the schedule data
        $values = [
            'customer_id' => $customer_id,
            'waf_rs_no' => $waf_rs_no,
            'eta_date' => $eta_date,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        // Update the schedule
        if (!$is_updated = $this->Schedule->update($where, $values)) {
            $this->_error = 'server_error';
            return false;
        }

        // Process trip data for updates
        $this->_process_trip_data_edit($schedule->id);

        return $is_updated;
    }

    /**
     * Attempt Edit Trip (for individual trip reschedule)
     */
    protected function _attempt_edit_trip($schedule, $trip)
    {
        $customer_id = $this->input->post('customer');
        $waf_rs_no = $this->input->post('waf_rs_no');
        $eta_date_input = $this->input->post('eta_date');

        $eta_date = date('Y-m-d H:i:s', strtotime($eta_date_input));

        // Validate customer exists
        $customer = $this->Customer->select('name', ['id' => $customer_id], 1);
        if (!$customer) {
            $this->_error = 'invalid_customer';
            return false;
        }

        // Update the parent schedule
        $schedule_where = [
            'id' => $schedule->id,
            'is_deleted' => 0
        ];

        $schedule_values = [
            'customer_id' => $customer_id,
            'waf_rs_no' => $waf_rs_no,
            'eta_date' => $eta_date,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$this->Schedule->update($schedule_where, $schedule_values)) {
            $this->_error = 'server_error';
            return false;
        }

        // Update the specific trip
        $trip_no = $this->input->post('trip_no_' . $trip->id);
        $eta_time = $this->input->post('eta_time_' . $trip->id);

        if (!empty($eta_time)) {
            $trip_values = [
                'trip_no' => $trip_no,
                'eta_time' => $eta_time,
                'updated_by' => $_SESSION['user']->id,
                'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
            ];

            $trip_where = [
                'id' => $trip->id,
                'schedule_id' => $schedule->id
            ];

            if (!$this->Schedule_list->update($trip_where, $trip_values)) {
                $this->_error = 'server_error';
                return false;
            }
        }

        return true;
    }

    /**
     * Process trip data for edit
     */
    protected function _process_trip_data_edit($schedule_id)
    {
        $post_data = $this->input->post();
        $existing_trip_ids = [];
        $updated_trips = [];

        // Process existing trips (update them)
        foreach ($post_data as $key => $value) {
            if (strpos($key, 'trip_item_') === 0) {
                $trip_id = $value;
                $existing_trip_ids[] = $trip_id;

                $trip_no = $this->input->post('trip_no_' . $trip_id);
                $eta_time = $this->input->post('eta_time_' . $trip_id);

                if (!empty($eta_time)) {
                    // Update existing trip
                    $trip_values = [
                        'trip_no' => $trip_no,
                        'eta_time' => $eta_time,
                        'updated_by' => $_SESSION['user']->id,
                        'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
                    ];

                    $this->Schedule_list->update(
                        ['id' => $trip_id, 'schedule_id' => $schedule_id],
                        $trip_values
                    );
                }
            }
        }

        // Process new trips (those added via JavaScript)
        $this->_process_new_trip_data($schedule_id);

        // Process deleted trips
        $this->_process_deleted_trips($schedule_id);
    }

    /**
     * Process new trip data (for trips added via JavaScript)
     */
    protected function _process_new_trip_data($schedule_id)
    {
        // Get schedule info to determine customer and date
        $schedule = $this->Schedule->select('', ['id' => $schedule_id], 1);
        if (!$schedule) {
            return;
        }

        $post_data = $this->input->post();
        $trip_counter = $this->_get_next_trip_number($schedule->customer_id, $schedule->eta_date);

        // Look for new trip data (these will have numeric keys from JavaScript)
        foreach ($post_data as $key => $value) {
            if (strpos($key, 'trip_item_new_') === 0) {
                $temp_id = str_replace('trip_item_new_', '', $key);
                $eta_time = $this->input->post('eta_time_new_' . $temp_id);

                if (!empty($eta_time)) {
                    $trip_values = [
                        'schedule_id' => $schedule_id,
                        'trip_no' => $trip_counter,
                        'eta_time' => $eta_time,
                        'added_by' => $_SESSION['user']->id,
                        'added_on' => (new DateTime())->format('Y-m-d H:i:s')
                    ];

                    $this->Schedule_list->insert($trip_values);
                    $trip_counter++; // Increment for next trip
                }
            }
        }
    }

    /**
     * Process deleted trips
     */
    protected function _process_deleted_trips($schedule_id)
    {
        $post_data = $this->input->post();

        // Look for deleted trip data
        foreach ($post_data as $key => $value) {
            if (strpos($key, 'deleted_trip_') === 0) {
                $trip_id = $value;

                // Soft delete the trip
                $this->Schedule_list->update(
                    ['id' => $trip_id, 'schedule_id' => $schedule_id],
                    [
                        'is_deleted' => 1,
                        'updated_by' => $_SESSION['user']->id,
                        'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
                    ]
                );
            }
        }
    }

    /**
     * Process trip data (for adding new schedules)
     */
    protected function _process_trip_data($schedule_id)
    {
        // Get schedule info to determine customer and date
        $schedule = $this->Schedule->select('', ['id' => $schedule_id], 1);
        if (!$schedule) {
            return;
        }

        $post_data = $this->input->post();
        $trip_counter = $this->_get_next_trip_number($schedule->customer_id, $schedule->eta_date);

        foreach ($post_data as $key => $value) {
            if (strpos($key, 'trip_item_') === 0) {
                $trip_id = $value;
                $eta_time = $this->input->post('eta_time_' . $trip_id);

                if (!empty($eta_time)) {
                    $trip_values = [
                        'schedule_id' => $schedule_id,
                        'trip_no' => $trip_counter,
                        'eta_time' => $eta_time,
                        'added_by' => $_SESSION['user']->id,
                        'added_on' => (new DateTime())->format('Y-m-d H:i:s')
                    ];

                    $this->Schedule_list->insert($trip_values);
                    $trip_counter++; // Increment for next trip
                }
            }
        }
    }

    /**
     * Get next trip number for customer on specific date
     */
    protected function _get_next_trip_number($customer_id, $eta_date)
    {
        // Extract just the date part (ignore time)
        $date_only = date('Y-m-d', strtotime($eta_date));

        // Find the highest trip number for this customer on this date
        $sql = "SELECT MAX(sl.trip_no) as max_trip_no
                FROM schedule_list sl
                INNER JOIN schedule s ON s.id = sl.schedule_id
                WHERE s.customer_id = ?
                AND DATE(s.eta_date) = ?
                AND sl.is_deleted = 0
                AND s.is_deleted = 0";

        $query = $this->db->query($sql, [$customer_id, $date_only]);
        $result = $query->row();

        // Return next trip number (start from 1 if no existing trips)
        return $result && $result->max_trip_no ? ($result->max_trip_no + 1) : 1;
    }

    /**
     * Delete Trip
     */
    public function delete($trip_id)
    {
        $where = [
            'id' => $trip_id,
            'is_deleted' => 0
        ];
        if (!$trip = $this->Schedule_list->select('', $where, 1)) {
            http_response_code(400);
            $response = [
                'message' => 'Trip is not found.'
            ];
        } elseif (!$this->_attempt_delete_trip($trip)) {
            http_response_code(400);
            $response = [
                'message' => $this->_error_msg()
            ];
        } else {
            $response = [
                'message' => 'Trip is successfully removed.'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Get next trip number for AJAX requests
     */
    public function get_next_trip_number()
    {
        $customer_id = $this->input->post('customer_id');
        $eta_date = $this->input->post('eta_date');

        if (empty($customer_id) || empty($eta_date)) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing customer_id or eta_date']);
            return;
        }

        $next_trip_no = $this->_get_next_trip_number($customer_id, $eta_date);

        echo json_encode(['next_trip_no' => $next_trip_no]);
    }

    /**
     * Attempt Delete Trip
     */
    protected function _attempt_delete_trip($trip)
    {
        $where = [
            'id' => $trip->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->Schedule_list->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }

    /**
     * Attempt Delete Schedule (Legacy - for entire schedule deletion)
     */
    protected function _attempt_delete($schedule)
    {
        $where = [
            'id' => $schedule->id
        ];
        $values = [
            'is_deleted' => 1,
            'updated_by' => $_SESSION['user']->id,
            'updated_on' => (new DateTime())->format('Y-m-d H:i:s')
        ];

        if (!$is_deleted = $this->Schedule->update($where, $values)) {
            $this->_error = 'server_error';
        }

        return $is_deleted;
    }
}
