<div class="sect">
	<div class="col-md-10 offset-md-1 form-group">
		<form role="form" method="post" class="crud-box" accept-charset="utf-8" enctype="multipart/form-data">
			<h1 class="crud-title">
				<?= $title; ?>
			</h1>

			<fieldset>
				<h5 class="crud-subtitle"><span>Edit schedule</span></h5>

				<div class="row">
					<div class="col-md-4 form-group">
						<label for="customer" class="control-label">Grower Farm Name/Place</label>

						<select id="nameSelect" class="form-control dropsearch" name="customer" data-placeholder="">
							<option></option>
							<?php foreach ($customers as $customer): ?>
								<option value="<?= $customer->id ?>" <?= set_select('customer', $customer->id, ($schedule->customer_id == $customer->id)); ?>>
									<?= ucwords($customer->name) ?>
								</option>
							<?php endforeach; ?>
						</select>
						<div class="red-text flash-message">
							<?= form_error('customer'); ?>
						</div>
					</div>

					<div class="col-md-4 form-group">
						<label for="waf_rs_no" class="control-label">WAF No. / RS No.</label>

						<input type="text" name="waf_rs_no" class="form-control"
							value="<?= set_value('waf_rs_no', $schedule->waf_rs_no); ?>">

						<div class="red-text flash-message">
							<?= form_error('waf_rs_no'); ?>
						</div>
					</div>
					<div class="col-md-4 form-group">
						<label for="eta_date" class="control-label">ETA Date</label>

						<input type="date" name="eta_date" class="form-control"
							value="<?= set_value('eta_date', date('Y-m-d', strtotime($schedule->eta_date))); ?>">

						<div class="red-text flash-message">
							<?= form_error('eta_date'); ?>
						</div>
					</div>
				</div>

				<h5 class="crud-subtitle"><span>List of Trip</span></h5>

				<div class="row">
					<div class="col-md-12 form-group">
						<table class="item-table" width="100%">
							<thead>
								<tr>
									<th class="text-center" style="width: 10%;">Trip No.</th>
									<th class="text-center" style="width: 20%;">ETA Time</th>
									<?php if ($title !== 'Reschedule'): ?>
										<th class="text-center" style="width: 10%;">Action</th>
									<?php endif; ?>
								</tr>
							</thead>
							<tbody>
								<?php if (empty($trip_data)): ?>
									<tr class="no-item">
										<td class="text-center" colspan="<?= ($title !== 'Reschedule') ? '3' : '2' ?>">No trips added yet.</td>
									</tr>
								<?php else: ?>
									<?php foreach ($trip_data as $index => $trip): ?>
										<tr class="trip_item">
											<td>
												<input type="hidden" name="trip_item_<?= $trip->id ?>" value="<?= $trip->id ?>">
												<input type="number" name="trip_no_<?= $trip->id ?>" class="form-control-plaintext" value="<?= $trip->trip_no ?>" readonly>
											</td>
											<td>
												<input type="time" name="eta_time_<?= $trip->id ?>" class="form-control" value="<?= $trip->eta_time ?>">
											</td>
											<td class="text-center">
												<?php if ($title !== 'Reschedule'): ?>
													<button type="button" class="btn btn-sm btn-danger action-delete-item">Delete</button>
												<?php else: ?>
													<!-- No actions for reschedule -->
												<?php endif; ?>
											</td>
										</tr>
									<?php endforeach; ?>
								<?php endif; ?>
							</tbody>
						</table>
					</div>
				</div>

				<?php if ($title !== 'Reschedule'): ?>
					<div class="row">
						<div class="col-md-12 form-group">
							<button type="button" id="btnSubmit" class="btn btn-sm btn-dark action-add-item">Add
								Trip</button>
						</div>
					</div>
				<?php endif; ?>

			<div class="error">
				<?= $form_error; ?>
			</div>

			<hr>

			<div class="clearfix text-center">
				<button type="submit" name="submit" class="btn btn-primary">Update</button>
				<a href="<?= site_url('schedules') ?>" class="btn btn-light">Close</a>
			</div>
			</fieldset>
		</form>
	</div>
</div>
